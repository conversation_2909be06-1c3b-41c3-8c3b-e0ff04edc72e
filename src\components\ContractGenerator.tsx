import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Printer } from "lucide-react";

interface ContractData {
  employeeName: string;
  employeeAddress: string;
  employeeBirthDate: string;
  startDate: string;
  employmentType: 'permanent' | 'temporary';
  temporaryUntil: string;
  temporaryReason: string;
  hasProbation: boolean;
  probationMonths: string;
  jobTitle: string;
  accountNumber: string;
  competenceDevelopment: string;
  hasOwnTools: boolean;
  hourlyWage: string;
  // Company details
  companyName: string;
  companyOrgNumber: string;
  companyAddress: string;
  // Work details
  workingHours: string;
  workingTime: string;
  pauseTime: string;
  overtimeRate: string;
  nightOvertimeRate: string;
  toolAllowance: string;
  mileageRate: string;
  // Benefits
  pensionProvider: string;
  pensionOrgNumber: string;
  insuranceProvider: string;
  insuranceOrgNumber: string;
  // Legal
  noticePeriod: string;
  contractDeadline: string;
  scheduleNotice: string;
  paymentDate: string;
}

const ContractGenerator = () => {
  const [contractData, setContractData] = useState<ContractData>({
    employeeName: '',
    employeeAddress: '',
    employeeBirthDate: '',
    startDate: '',
    employmentType: 'permanent',
    temporaryUntil: '',
    temporaryReason: 'Sesongarbeid i anleggsperioden',
    hasProbation: false,
    probationMonths: '6',
    jobTitle: 'Anleggsgartner',
    accountNumber: '',
    competenceDevelopment: 'Arbeidsgiver tilbyr nødvendig opplæring',
    hasOwnTools: false,
    hourlyWage: '300',
    // Company details
    companyName: 'Ringerike Landskap AS',
    companyOrgNumber: '***********',
    companyAddress: 'c/o Kim Tuvsjøen, Birchs vei 7, 3530 RØYSE',
    // Work details
    workingHours: '37,5',
    workingTime: '07:00 – 15:00',
    pauseTime: '30 min ubetalt',
    overtimeRate: '50',
    nightOvertimeRate: '100',
    toolAllowance: '1,85',
    mileageRate: '3,50',
    // Benefits
    pensionProvider: 'Storebrand',
    pensionOrgNumber: '***********',
    insuranceProvider: 'Gjensidige Forsikring ASA',
    insuranceOrgNumber: '***********',
    // Legal
    noticePeriod: '1 måned',
    contractDeadline: '7 dager',
    scheduleNotice: '2 uker',
    paymentDate: '5.'
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [showContract, setShowContract] = useState(false);

  const handleInputChange = (field: keyof ContractData, value: string | boolean) => {
    setContractData(prev => ({ ...prev, [field]: value }));
  };

  const generateContract = () => {
    setShowContract(true);
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
  };

  const handlePrint = () => {
    window.print();
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    // Ensure proper Norwegian date format (dd.mm.yyyy)
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
  };

  const contractContent = `
<div class="contract-document">
  <div class="contract-header">
    <h1 class="contract-title">ARBEIDSAVTALE</h1>
    <div class="company-logo">
      <img src="/RingerikeLandskap_logo.svg" alt="Ringerike Landskap AS" class="logo-image" />
    </div>
  </div>

  <div class="parties-section">
    <div class="party-block">
      <strong>Arbeidsgiver</strong><br/>
      ${contractData.companyName}<br/>
      Org.nr ${contractData.companyOrgNumber}<br/>
      ${contractData.companyAddress}
    </div>

    <div class="party-block">
      <strong>Arbeidstaker</strong><br/>
      Navn: ${contractData.employeeName || '_'.repeat(28)}<br/>
      Adresse: ${contractData.employeeAddress || '_'.repeat(25)}<br/>
      Fødselsdato: ${contractData.employeeBirthDate || '_'.repeat(21)}
    </div>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>1 Ansettelsesforhold</h2>
    <ul class="contract-list">
      <li><strong>Startdato:</strong> ${formatDate(contractData.startDate) || '_'.repeat(19)}</li>
      <li><strong>Ansettelsestype:</strong> ${contractData.employmentType === 'permanent' ? '☑ Fast ☐ Midlertidig' : '☐ Fast ☑ Midlertidig'}${contractData.employmentType === 'temporary' ? ` t.o.m. ${formatDate(contractData.temporaryUntil) || '_'.repeat(12)}` : ''}</li>
      ${contractData.employmentType === 'temporary' ? `<li><em>Grunnlag (hvis midlertidig): ${contractData.temporaryReason || '_'.repeat(33)}</em></li>` : ''}
      <li><strong>Prøvetid:</strong> ${contractData.hasProbation ? `☑ ${contractData.probationMonths || '___'} mnd` : '☑ Ingen'} (maks 6)</li>
      ${contractData.hasProbation ? '<li><em>Oppsigelsesfrist i prøvetid: 14 dager. Prøvetid kan forlenges ved fravær.</em></li>' : ''}
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>2 Arbeidssted</h2>
    <p>Prosjektbasert innen Ringerike, Hole, Jevnaker og Bærum kommuner; oppmøtested avtales for hvert prosjekt.</p>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>3 Stilling & oppgaver</h2>
    <p><strong>Stilling:</strong> ${contractData.jobTitle || '_'.repeat(31)}<br/>
    Arbeid innen anleggsgartner‑ og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten.</p>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>4 Arbeidstid & pauser</h2>
    <ul class="contract-list">
      <li><strong>Normal arbeidstid:</strong> ${contractData.workingHours} t per uke, normalt kl. ${contractData.workingTime}</li>
      <li><strong>Arbeidsplan:</strong> Fastsettes av arbeidsgiver etter prosjektbehov med rimelig varsel</li>
      <li><strong>Pauser:</strong> ${contractData.pauseTime} pause ved arbeidsdag over 5,5 t iht. AML § 10-9</li>
      <li><strong>Overtid:</strong> ${contractData.overtimeRate}% ordinær / ${contractData.nightOvertimeRate}% natt og helg etter AML § 10-6 (11)</li>
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>5 Lønn & godtgjørelse</h2>
    <table class="compensation-table">
      <tr>
        <td><strong>Element</strong></td>
        <td><strong>Sats / tidspunkt</strong></td>
      </tr>
      <tr>
        <td>Timesats (timelønn)</td>
        <td><strong>kr ${contractData.hourlyWage || '300'},-</strong></td>
      </tr>
      <tr>
        <td>Utbetaling</td>
        <td>${contractData.paymentDate} hver måned til konto nr.: ${contractData.accountNumber || '_'.repeat(10)} (elektronisk)</td>
      </tr>
      <tr>
        <td>Overtidstillegg</td>
        <td>${contractData.overtimeRate}% ordinær / ${contractData.nightOvertimeRate}% natt og helg (AML § 10-6 (11))</td>
      </tr>
      ${contractData.hasOwnTools ? `<tr><td>Verktøygodtgjørelse</td><td><strong>kr ${contractData.toolAllowance}/time</strong> (eget håndverktøy)</td></tr>` : ''}
      <tr>
        <td>Kjøring egen bil</td>
        <td>Statens trekkfrie sats – pt. <strong>${contractData.mileageRate} kr/km</strong></td>
      </tr>
      <tr>
        <td>Pensjon</td>
        <td>OTP ‑ ${contractData.pensionProvider} (org.nr ${contractData.pensionOrgNumber})</td>
      </tr>
    </table>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>6 Ferie & feriepenger</h2>
    <ul class="contract-list">
      <li><strong>5 uker ferie</strong> pr. år (Ferieloven).</li>
      <li>Feriepenger <strong>12 %</strong>; utbetales før hovedferie / ved fratreden når aktuelt.</li>
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>7 Oppsigelse</h2>
    <table class="termination-table">
      <tr>
        <td><strong>Situasjon</strong></td>
        <td><strong>Frist</strong></td>
      </tr>
      <tr>
        <td>I prøvetid</td>
        <td>14 dager</td>
      </tr>
      <tr>
        <td>Etter prøvetid</td>
        <td>${contractData.noticePeriod} gjensidig (øker med ansiennitet iht. AML § 15-3)</td>
      </tr>
    </table>
    <p class="legal-note"><strong>Fremgangsmåte:</strong> Oppsigelse skal være skriftlig iht. AML kap. 15 og 17.</p>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>8 Betalt fravær</h2>
    <ul class="contract-list">
      <li><strong>Sykdom:</strong> Arbeidsgiver dekker lønn i arbeidsgiverperioden (16 kalenderdager)</li>
      <li><strong>Omsorgspenger:</strong> Iht. folketrygdloven og AML kap. 12</li>
      <li><strong>Velferdspermisjon:</strong> Betalt kortvarig permisjon ved særlige behov iht. AML § 12-9</li>
      <li><strong>Øvrig betalt fravær:</strong> Følger gjeldende lovbestemmelser</li>
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>9 Kompetanseutvikling</h2>
    <p><strong>Opplæring:</strong> ${contractData.competenceDevelopment || 'Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen'} (AML § 14-6 (1) bokstav l)</p>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>10 Taushetsplikt</h2>
    <p>Arbeidstaker har taushetsplikt om forretningsforhold, kundelister, priskalkyler og designløsninger. Taushetsplikten gjelder også etter arbeidsforholdets opphør.</p>
  </div>

  <hr class="section-divider"/>

  <div class="contract-section">
    <h2>11 Diverse vilkår</h2>
    <ul class="contract-list">
      <li><strong>HMS og sikkerhet:</strong> Arbeidstaker følger virksomhetens HMS-rutiner og bruker påkrevd verneutstyr</li>
      <li>Arbeidsgiver stiller nødvendig arbeidstøy og verktøy</li>
      <li><strong>Tariffavtale:</strong> Ingen tariffavtale er gjeldende pr. dags dato (AML § 14-6 (1) bokstav j)</li>
      <li><strong>Yrkesskadeforsikring:</strong> ${contractData.insuranceProvider} (org.nr ${contractData.insuranceOrgNumber})</li>
      <li><strong>Kontraktfrist:</strong> Denne avtalen skal signeres innen ${contractData.contractDeadline} etter tiltredelse jf. AML § 14-5</li>
      <li><strong>Endringer i arbeidsplan:</strong> Varsles minimum ${contractData.scheduleNotice} i forveien der mulig</li>
      <li>Endringer i arbeidsforholdet dokumenteres skriftlig som vedlegg til denne avtalen</li>
    </ul>
  </div>

  <hr class="section-divider"/>

  <div class="signature-section">
    <h2>Signaturer</h2>
    <table class="signature-table">
      <tr>
        <td><strong>Sted / dato:</strong> Røyse, ${formatDate(contractData.startDate) || '_'.repeat(14)}</td>
        <td></td>
      </tr>
      <tr>
        <td><strong>Arbeidsgiver:</strong> _________________________________</td>
        <td><strong>Arbeidstaker:</strong> _________________________________</td>
      </tr>
    </table>
  </div>

  <hr class="section-divider"/>

  <div class="footer-note">
    <em>Avtalen er inngått i to eksemplarer; hver part beholder ett.</em>
  </div>
</div>

<style>
  .contract-document {
    font-family: 'Calibri', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.5;
    color: #333;
    max-width: 210mm;
    margin: 0 auto;
    padding: 20mm;
    background: white;
    font-size: 11pt;
    font-weight: 400;
  }
  
  .contract-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    position: relative;
  }

  .contract-title {
    flex: 1;
    text-align: center;
    font-size: 18pt;
    font-weight: 700;
    margin-bottom: 24px;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    color: #2c5234;
  }

  .company-logo {
    position: absolute;
    top: -40px;
    left: -20px;
    width: 80px;
    height: auto;
  }

  .logo-image {
    width: 100%;
    height: auto;
    max-width: 80px;
    max-height: 60px;
    object-fit: contain;
  }
  
  .parties-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 12pt;
  }
  
  .party-block {
    flex: 1;
    margin-right: 20px;
    line-height: 1.4;
  }
  
  .party-block:last-child {
    margin-right: 0;
  }
  
  .section-divider {
    border: none;
    border-top: 1px solid #e0e0e0;
    margin: 10px 0;
  }
  
  .contract-section {
    margin-bottom: 16px;
  }
  
  .contract-section h2 {
    font-size: 12pt;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c5234;
    letter-spacing: 0.3px;
  }
  
  .contract-section p {
    line-height: 1.5;
    margin-bottom: 8px;
  }
  
  .contract-list {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
  }
  
  .contract-list li {
    margin-bottom: 4px;
    line-height: 1.4;
  }
  
  .compensation-table, .termination-table, .signature-table {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
    font-size: 10.5pt;
  }
  
  .compensation-table td, .termination-table td {
    border: 1px solid #d0d0d0;
    padding: 6px 10px;
    vertical-align: top;
  }
  
  .signature-table td {
    padding: 12px 8px;
    vertical-align: bottom;
    border: none;
  }
  
  .legal-note {
    font-size: 10pt;
    margin-top: 6px;
    color: #666;
  }
  
  .signature-section {
    margin-top: 60px;
  }
  
  .signature-section h2 {
    margin-bottom: 12px;
  }
  
  .footer-note {
    text-align: center;
    font-size: 10pt;
    margin-top: 20px;
    color: #666;
    font-style: italic;
  }
  
  strong {
    font-weight: 600;
  }
  
  /* Print-specific styles */
  @media print {
    /* Set page margins for all pages with increased spacing */
    @page {
      margin: 30mm 15mm 25mm 15mm; /* top, right, bottom, left - increased top and bottom */
      size: A4;
    }

    /* Page numbering - simplified approach */
    @page {
      @bottom-right {
        content: counter(page) " / " counter(pages);
        font-family: 'Calibri', Arial, sans-serif;
        font-size: 10pt;
        color: #555;
        margin-right: 5mm;
        margin-bottom: 5mm;
      }
    }

    /* Alternative approach for browsers that don't support @page @bottom-right */
    body {
      counter-reset: page;
    }

    @page :left {
      margin-top: 30mm; /* Increased from 20mm */
    }

    @page :right {
      margin-top: 30mm; /* Increased from 20mm */
    }

    body * {
      visibility: hidden;
    }

    .contract-document, .contract-document * {
      visibility: visible;
    }

    .contract-document {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      padding: 0; /* Remove padding since we're using @page margins */
      margin: 0;
      font-size: 11pt;
      /* Remove page-break-inside: avoid from the main container */
    }

    .contract-header {
      margin-bottom: 20px;
    }

    .contract-title {
      font-size: 16pt;
      color: #000 !important;
      margin-top: 0;
      padding-top: 0;
    }

    .company-logo {
      width: 70px;
      top: -10px !important;
      right: -10px !important;
    }

    .logo-image {
      max-width: 70px;
      max-height: 50px;
      -webkit-print-color-adjust: exact;
      color-adjust: exact;
      print-color-adjust: exact;
    }

    .contract-section h2 {
      font-size: 12pt;
      color: #000 !important;
    }

    /* Better page break control */
    .parties-section {
      page-break-inside: avoid;
      break-inside: avoid;
    }

    .contract-section {
      page-break-inside: avoid;
      break-inside: avoid;
      margin-bottom: 12px;
    }

    .signature-section {
      page-break-inside: avoid;
      break-inside: avoid;
      page-break-before: auto;
    }

    /* Ensure tables don't break awkwardly */
    .compensation-table, .termination-table {
      page-break-inside: avoid;
      break-inside: avoid;
    }

    /* Add some spacing after page breaks */
    .contract-section:first-child {
      margin-top: 0;
    }

    /* Hide any UI elements that shouldn't print */
    .no-print {
      display: none !important;
    }
  }
</style>`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-50 p-4">
      <div className="max-w-4xl mx-auto">
        <Card className="shadow-lg">
          <CardHeader className="bg-gradient-to-r from-green-600 to-emerald-600 text-white">
            <CardTitle className="text-2xl font-bold text-center">
              Arbeidskontrakt Generator
            </CardTitle>
            <p className="text-center text-green-100 font-medium">Ringerike Landskap AS</p>
          </CardHeader>
          <CardContent className="p-6">
            {!showContract ? (
              <div className="space-y-6">
                {/* Step Indicator */}
                <div className="flex items-center justify-center space-x-4 mb-8">
                  <div className={`flex items-center space-x-2 ${currentStep >= 1 ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${currentStep >= 1 ? 'bg-green-600 text-white' : 'bg-gray-200'}`}>
                      1
                    </div>
                    <span className="font-medium">Grunninfo</span>
                  </div>
                  <div className={`w-8 h-1 ${currentStep >= 2 ? 'bg-green-600' : 'bg-gray-200'}`}></div>
                  <div className={`flex items-center space-x-2 ${currentStep >= 2 ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${currentStep >= 2 ? 'bg-green-600 text-white' : 'bg-gray-200'}`}>
                      2
                    </div>
                    <span className="font-medium">Avansert</span>
                  </div>
                  <div className={`w-8 h-1 ${currentStep >= 3 ? 'bg-green-600' : 'bg-gray-200'}`}></div>
                  <div className={`flex items-center space-x-2 ${currentStep >= 3 ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${currentStep >= 3 ? 'bg-green-600 text-white' : 'bg-gray-200'}`}>
                      3
                    </div>
                    <span className="font-medium">Generer</span>
                  </div>
                </div>

                {/* Step 1: Basic Information */}
                {currentStep === 1 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-800">Steg 1: Grunnleggende informasjon</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="employeeName">Navn på arbeidstaker *</Label>
                    <Input
                      id="employeeName"
                      value={contractData.employeeName}
                      onChange={(e) => handleInputChange('employeeName', e.target.value)}
                      placeholder="Fullt navn"
                    />
                  </div>
                  <div>
                    <Label htmlFor="employeeBirthDate">Fødselsdato *</Label>
                    <Input
                      id="employeeBirthDate"
                      value={contractData.employeeBirthDate}
                      onChange={(e) => handleInputChange('employeeBirthDate', e.target.value)}
                      placeholder="dd.mm.åååå"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="employeeAddress">Adresse *</Label>
                  <Input
                    id="employeeAddress"
                    value={contractData.employeeAddress}
                    onChange={(e) => handleInputChange('employeeAddress', e.target.value)}
                    placeholder="Gate/vei, postnummer og sted"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">Startdato *</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={contractData.startDate}
                      onChange={(e) => handleInputChange('startDate', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="jobTitle">Stillingstittel *</Label>
                    <Input
                      id="jobTitle"
                      value={contractData.jobTitle}
                      onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="hourlyWage">Timelønn (kr)</Label>
                    <Input
                      id="hourlyWage"
                      value={contractData.hourlyWage}
                      onChange={(e) => handleInputChange('hourlyWage', e.target.value)}
                      type="number"
                      min="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="accountNumber">Kontonummer</Label>
                    <Input
                      id="accountNumber"
                      value={contractData.accountNumber}
                      onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                      placeholder="XXXX.XX.XXXXX"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="competenceDevelopment">Kompetanseutvikling (valgfritt)</Label>
                  <Input
                    id="competenceDevelopment"
                    value={contractData.competenceDevelopment}
                    onChange={(e) => handleInputChange('competenceDevelopment', e.target.value)}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasOwnTools"
                    checked={contractData.hasOwnTools}
                    onCheckedChange={(checked) => handleInputChange('hasOwnTools', checked as boolean)}
                  />
                  <Label htmlFor="hasOwnTools">Holder eget håndverktøy (gir kr 1,85/time)</Label>
                </div>

                <Separator />

                <div className="flex items-center space-x-2 mb-4">
                  <Checkbox
                    id="showAdvanced"
                    checked={showAdvanced}
                    onCheckedChange={(checked) => setShowAdvanced(checked as boolean)}
                  />
                  <Label htmlFor="showAdvanced">Vis avanserte innstillinger (alle verdier)</Label>
                </div>

                {showAdvanced && (
                  <div className="space-y-6 p-4 bg-gray-50 rounded-lg">
                    <h3 className="text-lg font-semibold text-gray-800">Avanserte innstillinger</h3>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-700">Bedriftsinformasjon</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="companyName">Bedriftsnavn</Label>
                          <Input
                            id="companyName"
                            value={contractData.companyName}
                            onChange={(e) => handleInputChange('companyName', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="companyOrgNumber">Organisasjonsnummer</Label>
                          <Input
                            id="companyOrgNumber"
                            value={contractData.companyOrgNumber}
                            onChange={(e) => handleInputChange('companyOrgNumber', e.target.value)}
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="companyAddress">Bedriftsadresse</Label>
                        <Input
                          id="companyAddress"
                          value={contractData.companyAddress}
                          onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-700">Arbeidstid og godtgjørelser</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="workingHours">Arbeidstimer per uke</Label>
                          <Input
                            id="workingHours"
                            value={contractData.workingHours}
                            onChange={(e) => handleInputChange('workingHours', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="workingTime">Arbeidstid</Label>
                          <Input
                            id="workingTime"
                            value={contractData.workingTime}
                            onChange={(e) => handleInputChange('workingTime', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="pauseTime">Pausetid</Label>
                          <Input
                            id="pauseTime"
                            value={contractData.pauseTime}
                            onChange={(e) => handleInputChange('pauseTime', e.target.value)}
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <Label htmlFor="overtimeRate">Overtidstillegg (%)</Label>
                          <Input
                            id="overtimeRate"
                            value={contractData.overtimeRate}
                            onChange={(e) => handleInputChange('overtimeRate', e.target.value)}
                            type="number"
                          />
                        </div>
                        <div>
                          <Label htmlFor="nightOvertimeRate">Natt/helg tillegg (%)</Label>
                          <Input
                            id="nightOvertimeRate"
                            value={contractData.nightOvertimeRate}
                            onChange={(e) => handleInputChange('nightOvertimeRate', e.target.value)}
                            type="number"
                          />
                        </div>
                        <div>
                          <Label htmlFor="toolAllowance">Verktøygodtgjørelse (kr/t)</Label>
                          <Input
                            id="toolAllowance"
                            value={contractData.toolAllowance}
                            onChange={(e) => handleInputChange('toolAllowance', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="mileageRate">Kjøregodtgjørelse (kr/km)</Label>
                          <Input
                            id="mileageRate"
                            value={contractData.mileageRate}
                            onChange={(e) => handleInputChange('mileageRate', e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-700">Pensjon og forsikring</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="pensionProvider">Pensjonsleverandør</Label>
                          <Input
                            id="pensionProvider"
                            value={contractData.pensionProvider}
                            onChange={(e) => handleInputChange('pensionProvider', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="pensionOrgNumber">Pensjon org.nr</Label>
                          <Input
                            id="pensionOrgNumber"
                            value={contractData.pensionOrgNumber}
                            onChange={(e) => handleInputChange('pensionOrgNumber', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="insuranceProvider">Forsikringsleverandør</Label>
                          <Input
                            id="insuranceProvider"
                            value={contractData.insuranceProvider}
                            onChange={(e) => handleInputChange('insuranceProvider', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="insuranceOrgNumber">Forsikring org.nr</Label>
                          <Input
                            id="insuranceOrgNumber"
                            value={contractData.insuranceOrgNumber}
                            onChange={(e) => handleInputChange('insuranceOrgNumber', e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-700">Juridiske bestemmelser</h4>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <Label htmlFor="noticePeriod">Oppsigelsesfrist</Label>
                          <Input
                            id="noticePeriod"
                            value={contractData.noticePeriod}
                            onChange={(e) => handleInputChange('noticePeriod', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="contractDeadline">Kontraktfrist (dager)</Label>
                          <Input
                            id="contractDeadline"
                            value={contractData.contractDeadline}
                            onChange={(e) => handleInputChange('contractDeadline', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="scheduleNotice">Varsel arbeidsplan</Label>
                          <Input
                            id="scheduleNotice"
                            value={contractData.scheduleNotice}
                            onChange={(e) => handleInputChange('scheduleNotice', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="paymentDate">Lønnsutbetaling (dag i mnd)</Label>
                          <Input
                            id="paymentDate"
                            value={contractData.paymentDate}
                            onChange={(e) => handleInputChange('paymentDate', e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="temporary"
                      checked={contractData.employmentType === 'temporary'}
                      onCheckedChange={(checked) =>
                        handleInputChange('employmentType', checked ? 'temporary' : 'permanent')
                      }
                    />
                    <Label htmlFor="temporary">Midlertidig ansettelse</Label>
                  </div>

                  {contractData.employmentType === 'temporary' && (
                    <div className="ml-6 space-y-3">
                      <div>
                        <Label htmlFor="temporaryUntil">Midlertidig til dato</Label>
                        <Input
                          id="temporaryUntil"
                          type="date"
                          value={contractData.temporaryUntil}
                          onChange={(e) => handleInputChange('temporaryUntil', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="temporaryReason">Grunnlag for midlertidig ansettelse</Label>
                        <Textarea
                          id="temporaryReason"
                          value={contractData.temporaryReason}
                          onChange={(e) => handleInputChange('temporaryReason', e.target.value)}
                          rows={2}
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="probation"
                      checked={contractData.hasProbation}
                      onCheckedChange={(checked) => handleInputChange('hasProbation', checked as boolean)}
                    />
                    <Label htmlFor="probation">Prøvetid</Label>
                  </div>

                  {contractData.hasProbation && (
                    <div className="ml-6">
                      <Label htmlFor="probationMonths">Antall måneder (maks 6)</Label>
                      <Input
                        id="probationMonths"
                        type="number"
                        min="1"
                        max="6"
                        value={contractData.probationMonths}
                        onChange={(e) => handleInputChange('probationMonths', e.target.value)}
                      />
                    </div>
                  )}
                </div>

                <div className="flex justify-between mt-6">
                  <div></div>
                  <Button onClick={nextStep} className="bg-green-600 hover:bg-green-700">
                    Neste: Avanserte innstillinger →
                  </Button>
                </div>
              </div>
            )}

                {/* Step 2: Advanced Settings */}
                {currentStep === 2 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-800">Steg 2: Avanserte innstillinger</h3>
                    <p className="text-gray-600">Tilpass alle verdier i kontrakten etter behov</p>

                {showAdvanced && (
                  <div className="space-y-6 p-4 bg-gray-50 rounded-lg">
                    <h3 className="text-lg font-semibold text-gray-800">Avanserte innstillinger</h3>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-700">Bedriftsinformasjon</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="companyName">Bedriftsnavn</Label>
                          <Input
                            id="companyName"
                            value={contractData.companyName}
                            onChange={(e) => handleInputChange('companyName', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="companyOrgNumber">Organisasjonsnummer</Label>
                          <Input
                            id="companyOrgNumber"
                            value={contractData.companyOrgNumber}
                            onChange={(e) => handleInputChange('companyOrgNumber', e.target.value)}
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="companyAddress">Bedriftsadresse</Label>
                        <Input
                          id="companyAddress"
                          value={contractData.companyAddress}
                          onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-700">Arbeidstid og godtgjørelser</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="workingHours">Arbeidstimer per uke</Label>
                          <Input
                            id="workingHours"
                            value={contractData.workingHours}
                            onChange={(e) => handleInputChange('workingHours', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="workingTime">Arbeidstid</Label>
                          <Input
                            id="workingTime"
                            value={contractData.workingTime}
                            onChange={(e) => handleInputChange('workingTime', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="pauseTime">Pausetid</Label>
                          <Input
                            id="pauseTime"
                            value={contractData.pauseTime}
                            onChange={(e) => handleInputChange('pauseTime', e.target.value)}
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <Label htmlFor="overtimeRate">Overtidstillegg (%)</Label>
                          <Input
                            id="overtimeRate"
                            value={contractData.overtimeRate}
                            onChange={(e) => handleInputChange('overtimeRate', e.target.value)}
                            type="number"
                          />
                        </div>
                        <div>
                          <Label htmlFor="nightOvertimeRate">Natt/helg tillegg (%)</Label>
                          <Input
                            id="nightOvertimeRate"
                            value={contractData.nightOvertimeRate}
                            onChange={(e) => handleInputChange('nightOvertimeRate', e.target.value)}
                            type="number"
                          />
                        </div>
                        <div>
                          <Label htmlFor="toolAllowance">Verktøygodtgjørelse (kr/t)</Label>
                          <Input
                            id="toolAllowance"
                            value={contractData.toolAllowance}
                            onChange={(e) => handleInputChange('toolAllowance', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="mileageRate">Kjøregodtgjørelse (kr/km)</Label>
                          <Input
                            id="mileageRate"
                            value={contractData.mileageRate}
                            onChange={(e) => handleInputChange('mileageRate', e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-700">Pensjon og forsikring</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="pensionProvider">Pensjonsleverandør</Label>
                          <Input
                            id="pensionProvider"
                            value={contractData.pensionProvider}
                            onChange={(e) => handleInputChange('pensionProvider', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="pensionOrgNumber">Pensjon org.nr</Label>
                          <Input
                            id="pensionOrgNumber"
                            value={contractData.pensionOrgNumber}
                            onChange={(e) => handleInputChange('pensionOrgNumber', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="insuranceProvider">Forsikringsleverandør</Label>
                          <Input
                            id="insuranceProvider"
                            value={contractData.insuranceProvider}
                            onChange={(e) => handleInputChange('insuranceProvider', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="insuranceOrgNumber">Forsikring org.nr</Label>
                          <Input
                            id="insuranceOrgNumber"
                            value={contractData.insuranceOrgNumber}
                            onChange={(e) => handleInputChange('insuranceOrgNumber', e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-700">Juridiske bestemmelser</h4>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <Label htmlFor="noticePeriod">Oppsigelsesfrist</Label>
                          <Input
                            id="noticePeriod"
                            value={contractData.noticePeriod}
                            onChange={(e) => handleInputChange('noticePeriod', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="contractDeadline">Kontraktfrist (dager)</Label>
                          <Input
                            id="contractDeadline"
                            value={contractData.contractDeadline}
                            onChange={(e) => handleInputChange('contractDeadline', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="scheduleNotice">Varsel arbeidsplan</Label>
                          <Input
                            id="scheduleNotice"
                            value={contractData.scheduleNotice}
                            onChange={(e) => handleInputChange('scheduleNotice', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="paymentDate">Lønnsutbetaling (dag i mnd)</Label>
                          <Input
                            id="paymentDate"
                            value={contractData.paymentDate}
                            onChange={(e) => handleInputChange('paymentDate', e.target.value)}
                          />
                        </div>
                      </div>
                    </div>



                    <div className="flex justify-between mt-6">
                      <Button onClick={prevStep} variant="outline">
                        ← Tilbake: Grunninfo
                      </Button>
                      <Button onClick={nextStep} className="bg-green-600 hover:bg-green-700">
                        Neste: Generer kontrakt →
                      </Button>
                    </div>
                  </div>
                )}

                {/* Step 3: Generate Contract */}
                {currentStep === 3 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-800">Steg 3: Generer arbeidskontrakt</h3>
                    <p className="text-gray-600">Kontroller informasjonen og generer den endelige kontrakten</p>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <h4 className="font-medium text-green-800 mb-2">Sammendrag</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong>Arbeidstaker:</strong> {contractData.employeeName || 'Ikke oppgitt'}
                        </div>
                        <div>
                          <strong>Stillingstittel:</strong> {contractData.jobTitle}
                        </div>
                        <div>
                          <strong>Startdato:</strong> {contractData.startDate ? formatDate(contractData.startDate) : 'Ikke oppgitt'}
                        </div>
                        <div>
                          <strong>Timelønn:</strong> kr {contractData.hourlyWage},-
                        </div>
                        <div>
                          <strong>Ansettelsestype:</strong> {contractData.employmentType === 'permanent' ? 'Fast' : 'Midlertidig'}
                        </div>
                        <div>
                          <strong>Prøvetid:</strong> {contractData.hasProbation ? `${contractData.probationMonths} måneder` : 'Ingen'}
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <Button onClick={prevStep} variant="outline">
                        ← Tilbake: Avanserte innstillinger
                      </Button>
                      <Button onClick={generateContract} className="bg-green-600 hover:bg-green-700 font-semibold">
                        Generer arbeidskontrakt
                      </Button>
                    </div>
                  </div>
                )}

                {/* Step 3: Generate Contract */}
                {currentStep === 3 && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-800">Steg 3: Generer arbeidskontrakt</h3>
                    <p className="text-gray-600">Kontroller informasjonen og generer den endelige kontrakten</p>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <h4 className="font-medium text-green-800 mb-2">Sammendrag</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <strong>Arbeidstaker:</strong> {contractData.employeeName || 'Ikke oppgitt'}
                        </div>
                        <div>
                          <strong>Stillingstittel:</strong> {contractData.jobTitle}
                        </div>
                        <div>
                          <strong>Startdato:</strong> {contractData.startDate ? formatDate(contractData.startDate) : 'Ikke oppgitt'}
                        </div>
                        <div>
                          <strong>Timelønn:</strong> kr {contractData.hourlyWage},-
                        </div>
                        <div>
                          <strong>Ansettelsestype:</strong> {contractData.employmentType === 'permanent' ? 'Fast' : 'Midlertidig'}
                        </div>
                        <div>
                          <strong>Prøvetid:</strong> {contractData.hasProbation ? `${contractData.probationMonths} måneder` : 'Ingen'}
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <Button onClick={prevStep} variant="outline">
                        ← Tilbake: Avanserte innstillinger
                      </Button>
                      <Button onClick={generateContract} className="bg-green-600 hover:bg-green-700 font-semibold">
                        Generer arbeidskontrakt
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center no-print">
                  <h3 className="text-lg font-semibold text-green-800">Generert arbeidskontrakt</h3>
                  <div className="flex gap-2">
                    <Button onClick={handlePrint} className="bg-green-600 hover:bg-green-700 font-semibold">
                      <Printer className="w-4 h-4 mr-2" />
                      Skriv ut
                    </Button>
                    <Button onClick={() => {setShowContract(false); setCurrentStep(1);}} variant="outline">
                      Tilbake til skjema
                    </Button>
                  </div>
                </div>
                
                <div 
                  className="contract-display border rounded-lg bg-white shadow-sm"
                  style={{ 
                    maxHeight: '70vh', 
                    overflow: 'auto'
                  }}
                  dangerouslySetInnerHTML={{ __html: contractContent }}
                />
                
                <div className="text-sm text-gray-600 bg-green-50 p-3 rounded no-print border-l-4 border-green-400">
                  <strong>Juridisk korrekt:</strong> Denne kontrakten oppfyller alle krav i arbeidsmiljøloven § 14-6
                  etter lovendringene 1. juli 2024. Kontrakten skal signeres innen 7 dager etter oppstart (AML § 14-5).
                  Juridisk tekst gjennomgått av ekstern HR-rådgiver 2025-05-31.
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ContractGenerator;
