# Arbeidskontrakt Generator - Ringerike Landskap AS

En spesialisert webapplikasjon for å generere juridisk korrekte arbeidskontrakter for Ringerike Landskap AS.

## Oversikt

Dette systemet er designet spesifikt for **Ringerike Landskap AS** og genererer standardiserte arbeidskontrakter som oppfyller kravene i Arbeidsmiljøloven § 14-6. Grensesnittet er kun ment for å generere kontrakter for nye ansettelser innen samme firma.

## Hovedfunksjonalitet

### Skjemabasert datainnsamling
- Ansattes personopplysninger (navn, adresse, fødselsdato)
- Ansettelsesdetaljer (startdato, stillingstittel, kontonummer)
- Ansettelsestype (fast/midlertidig)
- Prøvetidskonfigurasjon

### Dynamisk kontraktgenerering
- Sanntids forhåndsvisning av kontrakt
- Automatisk utfylling av profesjonell kontraktmal
- Inkluderer firmalogoer og branding
- Juridisk korrekte norske arbeidskontrakter

### Profesjonell dokumentutgang
- Print-optimalisert A4-format
- Profesjonell styling med firmafargene
- Strukturerte seksjoner som dekker alle juridiske krav
- Klar for umiddelbar utskrift og signering

## Hardkodede firmaspesifikke elementer

- **Arbeidsgiver**: Ringerike Landskap AS (Org.nr 924 826 541)
- **Adresse**: Birchs vei 7, 3530 Røyse
- **Arbeidssted**: Prosjektbasert innen Ringerike og omegn
- **Arbeidstid**: 37,5 t/uke, normalt 07:00-15:00
- **Timesats**: kr 300,-
- **Kjøregodtgjørelse**: 3,50 kr/km (statens sats)
- **Pensjon**: OTP - minimum 2% av lønn
- **Feriepenger**: 12%
- **Oppsigelsesfrist**: 14 dager i prøvetid, 1 måned etter prøvetid

## Teknisk arkitektur

### Frontend Stack
- **React 18** med TypeScript
- **Vite** for byggeverktøy
- **Tailwind CSS** for styling
- **shadcn/ui** komponentbibliotek
- **React Hook Form** for skjemahåndtering
- **Zod** for validering

### Kontraktmal
Kontraktmalen er definert i `src/components/ContractGenerator.tsx` (linje 60-221) som en template literal med:
- HTML-struktur for komplett kontraktdokument
- Dynamisk innhold via template-variabler
- Innebygd CSS med print-spesifikke stiler
- Profesjonell typografi og layout

## Installasjon og kjøring

```bash
# Installer avhengigheter
npm install

# Start utviklingsserver
npm run dev

# Bygg for produksjon
npm run build
```

## Juridisk overholdelse

Kontraktene oppfyller alle krav i norsk arbeidsrett:
- Arbeidsmiljøloven § 14-6
- Obligatoriske elementer: minstelønn, oppsigelsesfrister, pensjonsordninger
- Korrekt håndtering av prøvetid (maks 6 måneder)
- Riktig overtidskompensasjon (≥40% tillegg)

## Lisens

Dette systemet er utviklet spesifikt for Ringerike Landskap AS og er ikke ment for generell bruk.
